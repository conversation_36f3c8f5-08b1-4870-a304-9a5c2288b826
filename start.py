#!/usr/bin/env python3
"""
启动脚本 - 选择运行简化版或MCP版
"""

import sys
import os
import asyncio

def show_menu():
    """显示菜单"""
    print("🚀 LLM + 工具调用系统")
    print("=" * 50)
    print("请选择要运行的版本:")
    print()
    print("1. 简化版演示 (推荐新手)")
    print("   - 直接集成数学工具")
    print("   - 单进程运行")
    print("   - 简单易懂")
    print()
    print("2. 简化版交互聊天")
    print("   - 直接集成数学工具")
    print("   - 交互式对话")
    print()
    print("3. MCP版演示 (真正的MCP协议)")
    print("   - 独立MCP服务器")
    print("   - 标准MCP协议")
    print("   - 服务器-客户端架构")
    print()
    print("4. MCP版交互聊天")
    print("   - 真正的MCP实现")
    print("   - 交互式对话")
    print()
    print("5. 测试MCP客户端")
    print("   - 测试MCP连接和工具调用")
    print()
    print("0. 退出")
    print("=" * 50)

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("请输入选择 (0-5): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            
            elif choice == "1":
                print("\n🎯 启动简化版演示...")
                os.system("python demo.py")
            
            elif choice == "2":
                print("\n💬 启动简化版交互聊天...")
                os.system("python run_chat.py")
            
            elif choice == "3":
                print("\n🎯 启动MCP版演示...")
                os.system("python mcp_tools/demo_mcp.py")
            
            elif choice == "4":
                print("\n💬 启动MCP版交互聊天...")
                os.system("python mcp_tools/run_mcp_chat.py")
            
            elif choice == "5":
                print("\n🧪 测试MCP客户端...")
                os.system("python mcp_tools/mcp_client.py")
            
            else:
                print("❌ 无效选择，请重新输入")
                continue
            
            print("\n" + "=" * 50)
            input("按回车键继续...")
            print()
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 运行出错: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()

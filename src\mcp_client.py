#!/usr/bin/env python3
"""
MCP客户端
用于连接MCP服务器并调用工具
"""

import asyncio
import json
import subprocess
import sys
from typing import Any, Dict, List, Optional
from mcp.client.session import ClientSession
from mcp.client.stdio import stdio_client
from mcp.types import CallToolRequest, ListToolsRequest

class MCPClient:
    """MCP客户端类"""

    def __init__(self, server_command: str, server_args: List[str]):
        self.server_command = server_command
        self.server_args = server_args
        self.session: Optional[ClientSession] = None
        self.process: Optional[subprocess.Popen] = None
        self.read_stream = None
        self.write_stream = None
    
    async def connect(self):
        """连接到MCP服务器"""
        try:
            # 启动MCP服务器进程
            self.process = subprocess.Popen(
                [self.server_command] + self.server_args,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 等待一下让服务器启动
            await asyncio.sleep(0.5)

            # 创建stdio客户端
            stdio_transport = stdio_client(
                self.process.stdout,
                self.process.stdin
            )

            # 获取流
            self.read_stream, self.write_stream = await stdio_transport.__aenter__()

            # 创建客户端会话
            self.session = ClientSession(self.read_stream, self.write_stream)

            # 初始化连接
            await self.session.initialize()

            print("✅ 成功连接到MCP服务器")
            return True

        except Exception as e:
            print(f"❌ 连接MCP服务器失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        if not self.session:
            raise RuntimeError("未连接到MCP服务器")
        
        try:
            result = await self.session.list_tools()
            return [tool.model_dump() for tool in result.tools]
        except Exception as e:
            print(f"❌ 获取工具列表失败: {e}")
            return []
    
    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> str:
        """调用MCP工具"""
        if not self.session:
            raise RuntimeError("未连接到MCP服务器")
        
        try:
            result = await self.session.call_tool(name, arguments)
            
            # 提取文本内容
            if result.content:
                text_contents = []
                for content in result.content:
                    if hasattr(content, 'text'):
                        text_contents.append(content.text)
                return "\n".join(text_contents)
            
            return "工具执行完成，但没有返回内容"
            
        except Exception as e:
            return f"工具调用失败: {e}"
    
    async def disconnect(self):
        """断开连接"""
        if self.session:
            try:
                await self.session.close()
            except:
                pass
            self.session = None
        
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                try:
                    self.process.kill()
                except:
                    pass
            self.process = None
        
        print("🔌 已断开MCP服务器连接")

# 测试函数
async def test_mcp_client():
    """测试MCP客户端"""
    client = MCPClient("python", ["src/math_server.py"])
    
    try:
        # 连接服务器
        if not await client.connect():
            return
        
        # 获取工具列表
        tools = await client.list_tools()
        print(f"📋 可用工具: {len(tools)} 个")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # 测试乘法工具
        print("\n🧮 测试乘法计算:")
        result = await client.call_tool("multiply", {"a": 6, "b": 7})
        print(f"结果: {result}")
        
        # 测试加法工具
        print("\n🧮 测试加法计算:")
        result = await client.call_tool("add", {"a": 10, "b": 15})
        print(f"结果: {result}")
        
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(test_mcp_client())

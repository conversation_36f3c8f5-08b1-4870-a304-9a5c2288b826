#!/usr/bin/env python3
"""
MCP数学计算服务器
提供基本的数学运算工具，包括加法、减法、乘法、除法等
"""

import asyncio
import json
import sys
from typing import Any, Dict, List, Optional
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
    Tool,
    TextContent,
    EmbeddedResource,
)

# 创建MCP服务器实例
server = Server("math-calculator")

@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """
    列出所有可用的数学工具
    """
    return [
        Tool(
            name="multiply",
            description="计算两个数的乘法",
            inputSchema={
                "type": "object",
                "properties": {
                    "a": {
                        "type": "number",
                        "description": "第一个数字"
                    },
                    "b": {
                        "type": "number", 
                        "description": "第二个数字"
                    }
                },
                "required": ["a", "b"]
            }
        ),
        Tool(
            name="add",
            description="计算两个数的加法",
            inputSchema={
                "type": "object",
                "properties": {
                    "a": {
                        "type": "number",
                        "description": "第一个数字"
                    },
                    "b": {
                        "type": "number",
                        "description": "第二个数字"
                    }
                },
                "required": ["a", "b"]
            }
        ),
        Tool(
            name="subtract",
            description="计算两个数的减法",
            inputSchema={
                "type": "object",
                "properties": {
                    "a": {
                        "type": "number",
                        "description": "被减数"
                    },
                    "b": {
                        "type": "number",
                        "description": "减数"
                    }
                },
                "required": ["a", "b"]
            }
        ),
        Tool(
            name="divide",
            description="计算两个数的除法",
            inputSchema={
                "type": "object",
                "properties": {
                    "a": {
                        "type": "number",
                        "description": "被除数"
                    },
                    "b": {
                        "type": "number",
                        "description": "除数"
                    }
                },
                "required": ["a", "b"]
            }
        ),
        Tool(
            name="power",
            description="计算数字的幂运算",
            inputSchema={
                "type": "object",
                "properties": {
                    "base": {
                        "type": "number",
                        "description": "底数"
                    },
                    "exponent": {
                        "type": "number",
                        "description": "指数"
                    }
                },
                "required": ["base", "exponent"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
    """
    处理工具调用请求
    """
    try:
        if name == "multiply":
            a = arguments.get("a")
            b = arguments.get("b")
            if a is None or b is None:
                raise ValueError("缺少必需的参数 a 或 b")
            
            result = a * b
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"计算结果: {a} × {b} = {result}"
                    )
                ]
            )
        
        elif name == "add":
            a = arguments.get("a")
            b = arguments.get("b")
            if a is None or b is None:
                raise ValueError("缺少必需的参数 a 或 b")
            
            result = a + b
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"计算结果: {a} + {b} = {result}"
                    )
                ]
            )
        
        elif name == "subtract":
            a = arguments.get("a")
            b = arguments.get("b")
            if a is None or b is None:
                raise ValueError("缺少必需的参数 a 或 b")
            
            result = a - b
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"计算结果: {a} - {b} = {result}"
                    )
                ]
            )
        
        elif name == "divide":
            a = arguments.get("a")
            b = arguments.get("b")
            if a is None or b is None:
                raise ValueError("缺少必需的参数 a 或 b")
            if b == 0:
                raise ValueError("除数不能为零")
            
            result = a / b
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"计算结果: {a} ÷ {b} = {result}"
                    )
                ]
            )
        
        elif name == "power":
            base = arguments.get("base")
            exponent = arguments.get("exponent")
            if base is None or exponent is None:
                raise ValueError("缺少必需的参数 base 或 exponent")
            
            result = base ** exponent
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"计算结果: {base}^{exponent} = {result}"
                    )
                ]
            )
        
        else:
            raise ValueError(f"未知的工具: {name}")
    
    except Exception as e:
        return CallToolResult(
            content=[
                TextContent(
                    type="text",
                    text=f"计算错误: {str(e)}"
                )
            ],
            isError=True
        )

async def main():
    """
    启动MCP服务器
    """
    # 使用stdio传输协议运行服务器
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="math-calculator",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                )
            )
        )

if __name__ == "__main__":
    asyncio.run(main())

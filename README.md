# LLM + 工具调用系统

这个项目包含两个版本的LLM工具调用系统：**简化版**和**真正的MCP版**。当用户输入数学计算请求时，LLM会自动调用相应的工具进行计算，然后返回结果。

## 两个版本对比

| 特性 | 简化版 | MCP版 |
|------|--------|-------|
| 架构 | 直接集成工具 | 服务器-客户端 |
| 协议 | 无 | 标准MCP协议 |
| 进程 | 单进程 | 多进程 |
| 复杂度 | 简单 | 中等 |
| 适用场景 | 学习、原型 | 生产、扩展 |

### 简化版架构
```
用户输入 → LLM聊天系统 → 数学工具 → 计算结果 → LLM → 用户
```

### MCP版架构
```
用户输入 → LLM客户端 ←→ MCP客户端 ←→ MCP服务器 ←→ 数学工具
```

## 功能特性

- 🤖 支持OpenRouter和OpenAI API
- 🧮 集成数学计算工具（加法、减法、乘法、除法、幂运算）
- 🔧 智能工具调用
- 💬 友好的聊天界面
- ⚙️ 环境变量配置

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

在`.env`文件中配置API密钥：

```env
# OpenRouter API配置
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_API_URL=https://openrouter.ai/api/v1
OPENROUTER_MODEL=anthropic/claude-sonnet-4
```

## 快速开始

### 🚀 一键启动（推荐）
```bash
python start.py
```
这会显示菜单，让你选择运行哪个版本。

### 📋 直接运行

#### 简化版
```bash
python demo.py                    # 演示
python run_chat.py                # 交互聊天
python src/math_tools.py          # 测试工具
```

#### MCP版
```bash
python mcp_tools/demo_mcp.py      # MCP演示
python mcp_tools/run_mcp_chat.py  # MCP交互聊天
python mcp_tools/mcp_client.py    # 测试MCP客户端
```

## 使用示例

运行演示脚本可以看到系统的完整工作流程：

```bash
python demo.py
```

实际运行结果：

```
📝 测试 1: 3乘以4等于多少？
🤖 助手: 正在思考...
🔧 调用工具: multiply with {'a': 3, 'b': 4}
🤖 助手: 3乘以4等于12。

📝 测试 2: 计算10加上20
🤖 助手: 正在思考...
🔧 调用工具: add with {'a': 10, 'b': 20}
🤖 助手: 计算完成！10加上20等于30。

📝 测试 3: 15除以3的结果是什么？
🤖 助手: 正在思考...
🔧 调用工具: divide with {'a': 15, 'b': 3}
🤖 助手: 15除以3的结果是5。

📝 测试 4: 2的3次方是多少？
🤖 助手: 正在思考...
🔧 调用工具: power with {'base': 2, 'exponent': 3}
🤖 助手: 2的3次方等于8。
```

## 项目结构

```
.
├── .env                      # 环境变量配置
├── requirements.txt          # Python依赖
├── start.py                  # 一键启动脚本
├── README.md                # 项目说明
├── USAGE.md                 # 使用说明
├── PROJECT_SUMMARY.md       # 项目总结
├── src/                     # 简化版
│   ├── llm_chat.py          # LLM聊天系统
│   └── math_tools.py        # 数学工具模块
├── demo.py                  # 简化版演示
├── run_chat.py             # 简化版交互聊天
└── mcp_tools/              # MCP版
    ├── README_MCP.md       # MCP版说明
    ├── simple_mcp_server.py # MCP服务器
    ├── mcp_client.py       # MCP客户端
    ├── llm_mcp_chat.py     # LLM+MCP聊天系统
    ├── demo_mcp.py         # MCP版演示
    └── run_mcp_chat.py     # MCP版交互聊天
```

## 支持的数学运算

- **multiply**: 乘法运算 (a × b)
- **add**: 加法运算 (a + b)
- **subtract**: 减法运算 (a - b)
- **divide**: 除法运算 (a ÷ b)
- **power**: 幂运算 (base^exponent)

## 工作原理

1. **用户输入**: 用户输入包含数学计算的问题
2. **LLM分析**: LLM分析用户意图，识别需要进行的数学运算
3. **工具调用**: LLM生成工具调用指令（USE_TOOL格式）
4. **工具执行**: 系统解析工具调用，执行相应的数学计算
5. **计算结果**: 数学工具执行计算并返回结果
6. **结果整合**: LLM接收计算结果，生成友好的回复给用户

## 扩展功能

你可以轻松扩展更多工具：

1. 在`src/math_tools.py`中添加新的工具定义
2. 实现对应的计算逻辑
3. 更新系统提示词以包含新工具的说明

## 故障排除

- 确保`.env`文件中的API密钥正确
- 检查网络连接是否正常
- 确保所有依赖都已正确安装
- 检查Python路径和模块导入是否正确

#!/usr/bin/env python3
"""
简化MCP客户端
用于连接简化MCP服务器并调用工具
"""

import asyncio
import json
import subprocess
import sys
import os
from typing import Any, Dict, List, Optional

class SimpleMCPClient:
    """简化MCP客户端类"""

    def __init__(self, server_command: str, server_args: List[str] = None):
        self.server_command = server_command
        self.server_args = server_args or []
        self.process: Optional[subprocess.Popen] = None
        self.request_id = 0
    
    def _get_next_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id

    async def _send_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送JSON-RPC请求"""
        request = {
            "jsonrpc": "2.0",
            "id": self._get_next_id(),
            "method": method
        }
        if params:
            request["params"] = params

        # 发送请求
        request_line = json.dumps(request) + "\n"
        self.process.stdin.write(request_line)
        self.process.stdin.flush()

        # 读取响应
        response_line = self.process.stdout.readline().strip()
        if not response_line:
            raise RuntimeError("服务器没有响应")

        response = json.loads(response_line)

        if "error" in response:
            raise RuntimeError(f"服务器错误: {response['error']['message']}")

        return response.get("result", {})

    async def connect(self):
        """连接到MCP服务器"""
        try:
            print(f"🔌 启动MCP服务器: {self.server_command} {' '.join(self.server_args)}")

            # 确保使用正确的路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if self.server_args:
                server_script = os.path.join(current_dir, self.server_args[0])
                args = [server_script] + self.server_args[1:]
            else:
                args = []

            # 启动MCP服务器进程
            self.process = subprocess.Popen(
                [self.server_command] + args,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=current_dir
            )

            # 等待一下让服务器启动
            await asyncio.sleep(0.5)

            # 检查进程是否还在运行
            if self.process.poll() is not None:
                stderr_output = self.process.stderr.read()
                raise RuntimeError(f"MCP服务器启动失败: {stderr_output}")

            # 发送初始化请求
            await self._send_request("initialize", {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "simple-mcp-client",
                    "version": "1.0.0"
                }
            })

            print("✅ 成功连接到MCP服务器")
            return True

        except Exception as e:
            print(f"❌ 连接MCP服务器失败: {e}")
            import traceback
            traceback.print_exc()
            await self.disconnect()
            return False
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        if not self.process:
            raise RuntimeError("未连接到MCP服务器")

        try:
            result = await self._send_request("tools/list")
            return result.get("tools", [])
        except Exception as e:
            print(f"❌ 获取工具列表失败: {e}")
            return []

    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> str:
        """调用MCP工具"""
        if not self.process:
            raise RuntimeError("未连接到MCP服务器")

        try:
            result = await self._send_request("tools/call", {
                "name": name,
                "arguments": arguments
            })

            # 提取文本内容
            content = result.get("content", [])
            if content:
                text_contents = []
                for item in content:
                    if item.get("type") == "text":
                        text_contents.append(item.get("text", ""))
                return "\n".join(text_contents)

            return "工具执行完成，但没有返回内容"

        except Exception as e:
            return f"工具调用失败: {e}"
    
    async def disconnect(self):
        """断开连接"""
        if self.process:
            try:
                self.process.terminate()
                # 等待进程结束
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.process.kill()
                    self.process.wait()
            except:
                pass
            self.process = None

        print("🔌 已断开MCP服务器连接")

# 测试函数
async def test_mcp_client():
    """测试简化MCP客户端"""
    client = SimpleMCPClient("python", ["simple_mcp_server.py"])
    
    try:
        # 连接服务器
        if not await client.connect():
            return
        
        # 获取工具列表
        tools = await client.list_tools()
        print(f"\n📋 可用工具: {len(tools)} 个")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # 测试各种数学运算
        test_cases = [
            ("multiply", {"a": 6, "b": 7}, "乘法测试"),
            ("add", {"a": 10, "b": 15}, "加法测试"),
            ("subtract", {"a": 20, "b": 8}, "减法测试"),
            ("divide", {"a": 15, "b": 3}, "除法测试"),
            ("power", {"base": 2, "exponent": 3}, "幂运算测试"),
            ("sqrt", {"number": 16}, "平方根测试")
        ]
        
        print("\n🧮 测试数学运算:")
        for tool_name, args, description in test_cases:
            print(f"\n{description}:")
            result = await client.call_tool(tool_name, args)
            print(f"结果: {result}")
        
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(test_mcp_client())

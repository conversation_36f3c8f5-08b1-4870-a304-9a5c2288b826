#!/usr/bin/env python3
"""
演示真正的MCP系统
"""

import sys
import os
import asyncio

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from llm_mcp_chat import LLMMCPChat

async def demo():
    """演示MCP系统功能"""
    print("🎯 真正的LLM + MCP工具系统演示")
    print("=" * 50)
    
    # 初始化聊天系统
    chat = LLMMCPChat()
    
    try:
        await chat.initialize()
        
        # 测试问题列表
        test_questions = [
            "3乘以4等于多少？",
            "计算10加上20",
            "15除以3的结果是什么？",
            "2的3次方是多少？",
            "100减去25等于多少？",
            "16的平方根是多少？"
        ]
        
        print("🧪 开始测试...")
        print("-" * 30)
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n📝 测试 {i}: {question}")
            print("🤖 助手: 正在思考...")
            
            try:
                response = await chat.process_message(question)
                print(f"🤖 助手: {response}")
            except Exception as e:
                print(f"❌ 处理失败: {e}")
            
            print("-" * 30)
        
        print("\n✅ 演示完成！")
        print("\n💡 MCP系统工作流程:")
        print("1. 启动MCP数学工具服务器")
        print("2. LLM客户端连接到MCP服务器")
        print("3. 从MCP服务器获取可用工具列表")
        print("4. 用户输入数学问题")
        print("5. LLM分析问题并生成MCP工具调用")
        print("6. 通过MCP协议调用服务器上的工具")
        print("7. MCP服务器执行计算并返回结果")
        print("8. LLM基于MCP返回的结果生成友好回复")
        print("9. 用户收到最终答案")
        
    finally:
        await chat.cleanup()

if __name__ == "__main__":
    asyncio.run(demo())

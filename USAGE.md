# 使用说明

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置环境变量
确保 `.env` 文件中有正确的API配置：
```env
OPENROUTER_API_KEY=your_api_key_here
OPENROUTER_API_URL=https://openrouter.ai/api/v1
OPENROUTER_MODEL=anthropic/claude-sonnet-4
```

### 3. 运行系统

#### 方式1：演示模式（推荐）
```bash
python demo.py
```
这会自动运行5个测试用例，展示系统的完整工作流程。

#### 方式2：交互式聊天
```bash
python run_chat.py
```
启动交互式聊天界面，可以与系统进行对话。

#### 方式3：测试数学工具
```bash
python src/math_tools.py
```
单独测试数学工具的功能。

## 系统特点

### ✅ 无需启动服务器
- 这个项目**不需要**单独启动MCP服务器
- 数学工具直接集成在聊天系统中
- 一个命令即可运行完整系统

### 🔧 支持的数学运算
- **乘法**: `3乘以4等于多少？`
- **加法**: `计算10加上20`
- **减法**: `100减去25等于多少？`
- **除法**: `15除以3的结果是什么？`
- **幂运算**: `2的3次方是多少？`

### 🤖 智能识别
LLM能够：
- 自动识别用户的数学计算需求
- 提取数字参数
- 选择正确的计算工具
- 生成友好的回复

## 工作原理

```
用户输入 → LLM分析 → 生成工具调用 → 执行计算 → 返回结果 → 友好回复
```

1. 用户输入数学问题
2. LLM分析并识别计算类型
3. 生成 `USE_TOOL: tool_name {"param": value}` 格式的指令
4. 系统解析并调用相应的数学工具
5. 工具执行计算并返回结果
6. LLM基于结果生成自然语言回复

## 示例对话

```
👤 用户: 3乘以4等于多少？
🤖 助手: 正在思考...
🔧 调用工具: multiply with {'a': 3, 'b': 4}
🤖 助手: 3乘以4等于12。

👤 用户: 2的3次方是多少？
🤖 助手: 正在思考...
🔧 调用工具: power with {'base': 2, 'exponent': 3}
🤖 助手: 2的3次方等于8。也就是说，2×2×2 = 8。
```

## 扩展功能

要添加新的数学工具：

1. 在 `src/math_tools.py` 中添加新工具定义
2. 实现计算逻辑
3. 在 `src/llm_chat.py` 的系统提示词中添加工具说明

## 故障排除

- **API调用失败**: 检查 `.env` 文件中的API密钥是否正确
- **模块导入错误**: 确保在项目根目录运行命令
- **网络连接问题**: 检查网络连接和防火墙设置

## 项目文件说明

- `demo.py`: 演示脚本，展示系统功能
- `run_chat.py`: 交互式聊天启动脚本
- `src/llm_chat.py`: 主要的LLM聊天系统
- `src/math_tools.py`: 数学工具模块
- `.env`: 环境变量配置文件
- `requirements.txt`: Python依赖包列表

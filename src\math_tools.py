#!/usr/bin/env python3
"""
数学工具模块
提供基本的数学计算功能
"""

import json
from typing import Any, Dict, List, Optional

class MathTools:
    """数学工具类"""
    
    def __init__(self):
        self.tools = {
            "multiply": {
                "name": "multiply",
                "description": "计算两个数的乘法",
                "parameters": ["a", "b"]
            },
            "add": {
                "name": "add", 
                "description": "计算两个数的加法",
                "parameters": ["a", "b"]
            },
            "subtract": {
                "name": "subtract",
                "description": "计算两个数的减法", 
                "parameters": ["a", "b"]
            },
            "divide": {
                "name": "divide",
                "description": "计算两个数的除法",
                "parameters": ["a", "b"]
            },
            "power": {
                "name": "power",
                "description": "计算数字的幂运算",
                "parameters": ["base", "exponent"]
            }
        }
    
    def list_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        return list(self.tools.values())
    
    def call_tool(self, name: str, arguments: Dict[str, Any]) -> str:
        """调用工具"""
        try:
            if name == "multiply":
                a = arguments.get("a")
                b = arguments.get("b")
                if a is None or b is None:
                    return "错误: 缺少必需的参数 a 或 b"
                
                result = a * b
                return f"计算结果: {a} × {b} = {result}"
            
            elif name == "add":
                a = arguments.get("a")
                b = arguments.get("b")
                if a is None or b is None:
                    return "错误: 缺少必需的参数 a 或 b"
                
                result = a + b
                return f"计算结果: {a} + {b} = {result}"
            
            elif name == "subtract":
                a = arguments.get("a")
                b = arguments.get("b")
                if a is None or b is None:
                    return "错误: 缺少必需的参数 a 或 b"
                
                result = a - b
                return f"计算结果: {a} - {b} = {result}"
            
            elif name == "divide":
                a = arguments.get("a")
                b = arguments.get("b")
                if a is None or b is None:
                    return "错误: 缺少必需的参数 a 或 b"
                if b == 0:
                    return "错误: 除数不能为零"
                
                result = a / b
                return f"计算结果: {a} ÷ {b} = {result}"
            
            elif name == "power":
                base = arguments.get("base")
                exponent = arguments.get("exponent")
                if base is None or exponent is None:
                    return "错误: 缺少必需的参数 base 或 exponent"
                
                result = base ** exponent
                return f"计算结果: {base}^{exponent} = {result}"
            
            else:
                return f"错误: 未知的工具 {name}"
        
        except Exception as e:
            return f"计算错误: {str(e)}"

# 测试函数
def test_math_tools():
    """测试数学工具"""
    tools = MathTools()
    
    print("📋 可用工具:")
    for tool in tools.list_tools():
        print(f"  - {tool['name']}: {tool['description']}")
    
    print("\n🧮 测试计算:")
    
    # 测试乘法
    result = tools.call_tool("multiply", {"a": 6, "b": 7})
    print(f"乘法: {result}")
    
    # 测试加法
    result = tools.call_tool("add", {"a": 10, "b": 15})
    print(f"加法: {result}")
    
    # 测试减法
    result = tools.call_tool("subtract", {"a": 20, "b": 8})
    print(f"减法: {result}")
    
    # 测试除法
    result = tools.call_tool("divide", {"a": 15, "b": 3})
    print(f"除法: {result}")
    
    # 测试幂运算
    result = tools.call_tool("power", {"base": 2, "exponent": 3})
    print(f"幂运算: {result}")

if __name__ == "__main__":
    test_math_tools()

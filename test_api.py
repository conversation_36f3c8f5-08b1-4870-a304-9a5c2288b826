#!/usr/bin/env python3
"""
测试API连接
"""

import asyncio
import os
import httpx
from dotenv import load_dotenv

load_dotenv()

async def test_openrouter_api():
    """测试OpenRouter API"""
    api_key = os.getenv("OPENROUTER_API_KEY")
    api_url = os.getenv("OPENROUTER_API_URL", "https://openrouter.ai/api/v1")
    model = os.getenv("OPENROUTER_MODEL", "anthropic/claude-3.5-sonnet")
    
    if not api_key:
        print("❌ 未找到OPENROUTER_API_KEY")
        return False
    
    print(f"🔑 API Key: {api_key[:20]}...")
    print(f"🌐 API URL: {api_url}")
    print(f"🤖 Model: {model}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model,
        "messages": [
            {"role": "user", "content": "Hello, please respond with 'API test successful'"}
        ],
        "max_tokens": 50
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{api_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30.0
            )
            
            print(f"📊 Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                print(f"✅ API测试成功！")
                print(f"🤖 响应: {content}")
                return True
            else:
                print(f"❌ API测试失败: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ API测试出错: {e}")
        return False

async def main():
    print("🧪 测试API连接...")
    await test_openrouter_api()

if __name__ == "__main__":
    asyncio.run(main())

# 真正的MCP工具系统

这是一个基于真正MCP（Model Context Protocol）协议的LLM工具调用系统。与简化版本不同，这个系统实现了完整的MCP服务器-客户端架构。

## 系统架构

```
LLM客户端 ←→ MCP客户端 ←→ MCP服务器 ←→ 数学工具
```

## 核心组件

### 1. MCP服务器 (`simple_mcp_server.py`)
- 基于JSON-RPC协议的MCP服务器
- 提供6个数学工具：加法、减法、乘法、除法、幂运算、平方根
- 支持标准MCP协议消息

### 2. MCP客户端 (`mcp_client.py`)
- 连接到MCP服务器
- 获取工具列表
- 调用工具并返回结果

### 3. LLM聊天系统 (`llm_mcp_chat.py`)
- 集成MCP客户端
- 智能识别用户需求
- 自动调用MCP工具

## 使用方法

### 快速演示
```bash
python demo_mcp.py
```

### 交互式聊天
```bash
python run_mcp_chat.py
```

### 测试MCP客户端
```bash
python mcp_client.py
```

## MCP协议特点

### ✅ 真正的MCP实现
- **服务器独立运行**: MCP服务器作为独立进程运行
- **标准协议**: 基于JSON-RPC 2.0的MCP协议
- **工具发现**: 客户端从服务器动态获取工具列表
- **错误处理**: 完整的错误处理机制

### 🔧 支持的MCP消息
- `initialize`: 初始化连接
- `tools/list`: 获取工具列表
- `tools/call`: 调用工具

### 📋 工具列表
1. **multiply**: 乘法运算 (a × b)
2. **add**: 加法运算 (a + b)
3. **subtract**: 减法运算 (a - b)
4. **divide**: 除法运算 (a ÷ b)
5. **power**: 幂运算 (base^exponent)
6. **sqrt**: 平方根运算 (√number)

## 工作流程

1. **启动MCP服务器**: 独立的数学工具服务器进程
2. **客户端连接**: LLM系统通过MCP客户端连接服务器
3. **工具发现**: 从服务器获取可用工具列表
4. **动态提示词**: 根据工具列表生成LLM系统提示词
5. **用户交互**: 用户输入数学问题
6. **智能识别**: LLM分析并生成工具调用
7. **MCP调用**: 通过MCP协议调用服务器工具
8. **结果返回**: 服务器执行计算并返回结果
9. **友好回复**: LLM基于结果生成自然语言回复

## 示例对话

```
📝 测试: 3乘以4等于多少？
🤖 助手: 正在思考...
🔧 调用MCP工具: multiply with {'a': 3, 'b': 4}
🤖 助手: 3乘以4等于12。

📝 测试: 16的平方根是多少？
🤖 助手: 正在思考...
🔧 调用MCP工具: sqrt with {'number': 16}
🤖 助手: 16的平方根是4。也就是说，4 × 4 = 16，所以√16 = 4。
```

## 与简化版的区别

| 特性 | 简化版 | MCP版 |
|------|--------|-------|
| 架构 | 直接集成 | 服务器-客户端 |
| 协议 | 无 | JSON-RPC MCP |
| 进程 | 单进程 | 多进程 |
| 工具发现 | 静态 | 动态 |
| 扩展性 | 有限 | 高 |
| 标准化 | 无 | MCP标准 |

## 扩展功能

### 添加新工具
1. 在 `simple_mcp_server.py` 中添加工具定义
2. 实现工具处理逻辑
3. 客户端会自动发现新工具

### 添加新服务器
1. 实现MCP协议的新服务器
2. 修改客户端连接参数
3. 系统会自动适配新的工具集

## 技术细节

### JSON-RPC消息格式
```json
// 请求
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "multiply",
    "arguments": {"a": 3, "b": 4}
  }
}

// 响应
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "content": [
      {"type": "text", "text": "乘法计算: 3 × 4 = 12"}
    ]
  }
}
```

### 错误处理
- 连接错误自动重试
- 工具调用错误返回友好消息
- 服务器崩溃自动检测

## 故障排除

- **服务器启动失败**: 检查Python路径和依赖
- **连接超时**: 增加连接等待时间
- **工具调用失败**: 检查参数格式和类型

这个MCP实现展示了真正的工具服务器架构，为构建更复杂的AI工具系统提供了基础。

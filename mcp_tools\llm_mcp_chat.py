#!/usr/bin/env python3
"""
LLM + MCP聊天系统
使用真正的MCP协议连接工具服务器
"""

import asyncio
import json
import os
import re
import sys
from typing import Any, Dict, List, Optional
import httpx
from dotenv import load_dotenv
from mcp_client import SimpleMCPClient

# 加载环境变量
load_dotenv()

class LLMMCPChat:
    """LLM + MCP聊天系统类"""
    
    def __init__(self):
        # 配置API
        self.openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
        self.openrouter_api_url = os.getenv("OPENROUTER_API_URL", "https://openrouter.ai/api/v1")
        self.openrouter_model = os.getenv("OPENROUTER_MODEL", "anthropic/claude-sonnet-4")
        
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.openai_model = os.getenv("OPENAI_MODEL", "gpt-4")
        
        # 初始化MCP客户端
        self.mcp_client = SimpleMCPClient("python", ["simple_mcp_server.py"])
        self.mcp_connected = False
        self.available_tools = []
        
        # 系统提示词（将在连接MCP后动态生成）
        self.system_prompt = ""
    
    async def initialize(self):
        """初始化聊天系统"""
        print("🚀 正在初始化LLM + MCP聊天系统...")
        
        # 连接MCP服务器
        if await self.mcp_client.connect():
            self.mcp_connected = True
            
            # 获取工具列表
            self.available_tools = await self.mcp_client.list_tools()
            print(f"✅ MCP服务器已连接，可用工具: {len(self.available_tools)} 个")
            
            for tool in self.available_tools:
                print(f"  - {tool['name']}: {tool['description']}")
            
            # 生成系统提示词
            self._generate_system_prompt()
            
        else:
            print("⚠️  MCP服务器连接失败，将无法使用计算工具")
        
        print("✅ LLM + MCP聊天系统初始化完成\n")
    
    def _generate_system_prompt(self):
        """根据MCP工具动态生成系统提示词"""
        tools_description = ""
        for tool in self.available_tools:
            schema = tool.get('inputSchema', {})
            properties = schema.get('properties', {})
            required = schema.get('required', [])
            
            params_desc = []
            for param_name, param_info in properties.items():
                param_type = param_info.get('type', 'unknown')
                param_desc = param_info.get('description', '')
                is_required = param_name in required
                req_marker = " (必需)" if is_required else " (可选)"
                params_desc.append(f"  - {param_name} ({param_type}): {param_desc}{req_marker}")
            
            tools_description += f"- {tool['name']}: {tool['description']}\n"
            if params_desc:
                tools_description += "\n".join(params_desc) + "\n"
            tools_description += "\n"
        
        self.system_prompt = f"""你是一个智能助手，可以帮助用户进行数学计算。

当用户需要进行数学运算时，你应该：
1. 识别用户的计算需求
2. 使用相应的MCP工具进行计算
3. 将计算结果以友好的方式返回给用户

可用的MCP工具：
{tools_description}

当你需要使用工具时，请按以下格式回复：
USE_TOOL: tool_name {{"param1": value1, "param2": value2}}

例如：
- 用户问"3乘以4等于多少"，你应该回复：USE_TOOL: multiply {{"a": 3, "b": 4}}
- 用户问"10加上20"，你应该回复：USE_TOOL: add {{"a": 10, "b": 20}}
- 用户问"16的平方根"，你应该回复：USE_TOOL: sqrt {{"number": 16}}

请确保识别用户输入中的数字，并使用正确的工具和参数。
"""
    
    async def call_llm(self, messages: List[Dict[str, str]]) -> str:
        """调用LLM API"""
        try:
            # 优先使用OpenRouter
            if self.openrouter_api_key:
                return await self._call_openrouter(messages)
            elif self.openai_api_key:
                return await self._call_openai(messages)
            else:
                return "❌ 未配置API密钥，请在.env文件中设置OPENROUTER_API_KEY或OPENAI_API_KEY"
        
        except Exception as e:
            return f"❌ LLM调用失败: {e}"
    
    async def _call_openrouter(self, messages: List[Dict[str, str]]) -> str:
        """调用OpenRouter API"""
        headers = {
            "Authorization": f"Bearer {self.openrouter_api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.openrouter_model,
            "messages": [{"role": "system", "content": self.system_prompt}] + messages,
            "max_tokens": 1000,
            "temperature": 0.7
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.openrouter_api_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30.0
            )
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
    
    async def _call_openai(self, messages: List[Dict[str, str]]) -> str:
        """调用OpenAI API"""
        headers = {
            "Authorization": f"Bearer {self.openai_api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.openai_model,
            "messages": [{"role": "system", "content": self.system_prompt}] + messages,
            "max_tokens": 1000,
            "temperature": 0.7
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30.0
            )
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
    
    def parse_tool_call(self, response: str) -> Optional[tuple]:
        """解析工具调用"""
        # 匹配 USE_TOOL: tool_name {"param": value} 格式
        pattern = r'USE_TOOL:\s*(\w+)\s*(\{.*?\})'
        match = re.search(pattern, response)
        
        if match:
            tool_name = match.group(1)
            try:
                arguments = json.loads(match.group(2))
                return tool_name, arguments
            except json.JSONDecodeError:
                return None
        
        return None
    
    async def process_message(self, user_message: str) -> str:
        """处理用户消息"""
        messages = [{"role": "user", "content": user_message}]
        
        # 调用LLM
        llm_response = await self.call_llm(messages)
        
        # 检查是否需要使用工具
        tool_call = self.parse_tool_call(llm_response)
        
        if tool_call and self.mcp_connected:
            tool_name, arguments = tool_call
            print(f"🔧 调用MCP工具: {tool_name} with {arguments}")
            
            # 调用MCP工具
            tool_result = await self.mcp_client.call_tool(tool_name, arguments)
            
            # 将工具结果返回给LLM生成最终回复
            messages.extend([
                {"role": "assistant", "content": llm_response},
                {"role": "user", "content": f"MCP工具执行结果: {tool_result}。请基于这个结果给用户一个友好的回复。"}
            ])
            
            final_response = await self.call_llm(messages)
            return final_response
        
        return llm_response
    
    async def chat_loop(self):
        """聊天循环"""
        print("💬 LLM + MCP聊天系统已启动！输入 'quit' 退出")
        print("📝 你可以问我数学计算问题，比如：")
        print("   - 3乘以4等于多少？")
        print("   - 计算10加上20")
        print("   - 15除以3的结果是什么？")
        print("   - 2的3次方是多少？")
        print("   - 16的平方根是多少？")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n👤 用户: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    break
                
                if not user_input:
                    continue
                
                print("🤖 助手: 正在思考...")
                response = await self.process_message(user_input)
                print(f"🤖 助手: {response}")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ 处理消息时出错: {e}")
        
        print("\n👋 再见！")
    
    async def cleanup(self):
        """清理资源"""
        if self.mcp_connected:
            await self.mcp_client.disconnect()

async def main():
    """主函数"""
    chat = LLMMCPChat()
    
    try:
        await chat.initialize()
        await chat.chat_loop()
    finally:
        await chat.cleanup()

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
演示LLM + 数学工具系统
"""

import sys
import os
import asyncio

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.simple_llm_chat import SimpleLLMChat

async def demo():
    """演示系统功能"""
    print("🎯 LLM + 数学工具系统演示")
    print("=" * 50)
    
    # 初始化聊天系统
    chat = SimpleLLMChat()
    chat.initialize()
    
    # 测试问题列表
    test_questions = [
        "3乘以4等于多少？",
        "计算10加上20",
        "15除以3的结果是什么？",
        "2的3次方是多少？",
        "100减去25等于多少？"
    ]
    
    print("🧪 开始测试...")
    print("-" * 30)
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n📝 测试 {i}: {question}")
        print("🤖 助手: 正在思考...")
        
        try:
            response = await chat.process_message(question)
            print(f"🤖 助手: {response}")
        except Exception as e:
            print(f"❌ 处理失败: {e}")
        
        print("-" * 30)
    
    print("\n✅ 演示完成！")
    print("\n💡 系统工作流程:")
    print("1. 用户输入数学问题")
    print("2. LLM分析问题并识别需要的计算")
    print("3. LLM生成工具调用指令 (USE_TOOL格式)")
    print("4. 系统解析指令并调用相应的数学工具")
    print("5. 数学工具执行计算并返回结果")
    print("6. LLM基于计算结果生成友好的回复")
    print("7. 用户收到最终答案")

if __name__ == "__main__":
    asyncio.run(demo())
